{"name": "@mainframe-peru/common-core", "version": "1.0.0", "description": "Utilities used across all mainframe projects.", "main": "build/index.js", "repository": "https://github.com/Mainframe-Peru/common-core", "scripts": {"build": "tsc -p tsconfig.json", "format": "prettier . --check", "formatfix": "prettier . --write", "lint": "eslint src", "lintfix": "npm run lint -- --fix", "test": "jest", "prepare": "husky"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "devDependencies": {"@aws-sdk/client-sns": "^3.616.0", "@eslint/js": "^9.2.0", "@shopify/jest-koa-mocks": "^5.2.0", "@types/aws-lambda": "^8.10.141", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.12", "@types/koa": "^2.15.0", "@types/koa__router": "^12.0.4", "@types/node": "^20.12.11", "eslint": "^8.57.0", "globals": "^15.1.0", "husky": "^9.0.11", "jest": "^29.7.0", "prettier": "3.2.5", "ts-jest": "^29.1.4", "typescript-eslint": "^7.8.0"}, "engines": {"node": ">= 20.0.0"}, "files": ["build/**/*.js", "build/**/*.d.ts"], "publishConfig": {"registry": "https://npm.pkg.github.com"}, "release": {"branches": ["main", {"name": "beta", "prerelease": true}]}, "dependencies": {"@koa/router": "^13.1.0", "bcryptjs": "^2.4.3", "jose": "^5.3.0", "koa": "^2.15.3", "zod": "^3.23.8"}, "peerDependencies": {"@aws-sdk/client-sqs": "^3.848.0", "@mainframe-peru/types": "^1.86.6"}}