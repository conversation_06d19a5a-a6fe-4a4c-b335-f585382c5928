import { PolicyModules } from "../../../../types/main.types";
import { AdminPolicies } from "./types";

export const adminPoliciesConstant: PolicyModules<AdminPolicies> = {
  admin: {
    PUT_ADMIN: {
      value: 1 << 0,
      description: "El admin puede crear/editar perfiles de Admin",
    },
    READ_ADMIN: {
      value: 1 << 1,
      description: "El admin puede ver perfiles de Admins",
    },
    DELETE_ADMIN: {
      value: 1 << 2,
      description: "El admin puede eliminar Admins",
    },
    LIST_ADMINS: {
      value: 1 << 3,
      description: "El admin puede listar todos los Admins",
    },
    REPORT_ADMIN: {
      value: 1 << 4,
      description: "some description",
    },
    ELEVATE_ADMIN: {
      value: 1 << 5,
      description: "some description",
    },
  },
  user: {
    PUT_USER: {
      value: 1 << 0,
      description: "El admin puede crear/editar perfiles de influencer",
    },
    READ_USER: {
      value: 1 << 1,
      description: "El admin puede ver perfiles de influencers",
    },
    DELETE_USER: {
      value: 1 << 2,
      description: "El admin puede eliminar influencers",
    },
    LIST_USERS: {
      value: 1 << 3,
      description: "El admin puede listar todos los influencers",
    },
  },
  influencer: {
    READ_INFLUENCER: {
      value: 1 << 1,
      description: "El admin puede ver perfiles de influencers",
    },
    UPDATE_INFLUENCER: {
      value: 1 << 2,
      description: "El admin puede actualizar su propio influencer",
    },
  },
  transaction: {
    PUT_TRANSACTION: {
      value: 1 << 0,
      description: "some description",
    },
    LIST_TRANSACTIONS: {
      value: 1 << 1,
      description: "some description",
    },
    READ_TRANSACTION: {
      value: 1 << 2,
      description: "some description",
    },
  },
  card: {
    PUT_CARD: {
      value: 1 << 0,
      description: "El admin puede crear/editar tarjetas",
    },
    READ_CARD: {
      value: 1 << 1,
      description: "El admin puede ver tarjetas",
    },
    DELETE_CARD: {
      value: 1 << 2,
      description: "El admin puede eliminar tarjetas",
    },
    LIST_CARDS: {
      value: 1 << 3,
      description: "El admin puede listar todos los tarjetas de un usuario",
    },
  },
  plan: {
    PUT_PLAN: {
      value: 1 << 0,
      description: "El admin puede crear/editar planes",
    },
    READ_PLAN: {
      value: 1 << 1,
      description: "El admin puede ver planes",
    },
    DELETE_PLAN: {
      value: 1 << 2,
      description: "El admin puede eliminar planes",
    },
    LIST_PLANS: {
      value: 1 << 3,
      description: "El admin puede listar todos los planes",
    },
  },
  recurrence: {
    PUT_RECURRENCE: {
      value: 1 << 0,
      description: "El admin puede crear/editar sus recurrencias",
    },
    READ_RECURRENCE: {
      value: 1 << 1,
      description: "El admin puede ver sus recurrencias",
    },
    DELETE_RECURRENCE: {
      value: 1 << 2,
      description: "El admin puede eliminar sus recurrencias",
    },
    LIST_RECURRENCES: {
      value: 1 << 3,
      description: "El admin puede listar todos los sus recurrencias",
    },
  },
  invoice: {
    PUT_INVOICE: {
      value: 1 << 0,
      description: "El admin puede generar y crear boletas/facturas",
    },
    SEND_INVOICE: {
      value: 1 << 1,
      description: "El admin puede enviar boletas/facturas",
    },
  },
  complaint: {
    LIST_ISSUES: {
      value: 1 << 0,
      description: "El admin puede ver los reclamos y quejas",
    },
    PUT_ISSUE: {
      value: 1 << 1,
      description: "El admin puede resolver los reclamos y quejas",
    },
  },
  paymentProviderEvent: {
    GET_EVENT: {
      value: 1 << 0,
      description: "El admin puede obtener el evento del proveedor de pagos.",
    },
  },
  event: {
    CREATE_EVENT: {
      value: 1 << 0,
      description: "El admin puede crear eventos",
    },
    GET_EVENT: {
      value: 1 << 1,
      description: "El admin puede ver un evento",
    },
    UPDATE_EVENT: {
      value: 1 << 2,
      description: "El admin puede actualizar un evento",
    },
    DELETE_EVENT: {
      value: 1 << 3,
      description: "El admin puede eliminar un evento",
    },
    LIST_EVENTS: {
      value: 1 << 4,
      description: "El admin puede listar los eventos",
    },
  },
  business: {
    PUT_BUSINESS: {
      value: 1 << 0,
      description: "El admin puede crear/editar negocios",
    },
    READ_BUSINESS: {
      value: 1 << 1,
      description: "El admin puede ver negocios",
    },
    DELETE_BUSINESS: {
      value: 1 << 2,
      description: "El admin puede eliminar negocios",
    },
    LIST_BUSINESS: {
      value: 1 << 3,
      description: "El admin puede listar todos los negocios",
    },
  },
  businessPromotion: {
    PUT_PROMOTION: {
      value: 1 << 0,
      description: "El admin puede crear/editar promociones",
    },
    READ_PROMOTION: {
      value: 1 << 1,
      description: "El admin puede ver promociones",
    },
    DELETE_PROMOTION: {
      value: 1 << 2,
      description: "El admin puede eliminar promociones",
    },
    LIST_PROMOTION: {
      value: 1 << 3,
      description: "El admin puede listar todas las promociones",
    },
  },
  businessPromotionCode: {
    PUT_PROMOTION_CODE: {
      value: 1 << 0,
      description: "El admin puede crear/editar códigos de promociones",
    },
    READ_PROMOTION_CODE: {
      value: 1 << 1,
      description: "El admin puede ver códigos de promociones",
    },
    DELETE_PROMOTION_CODE: {
      value: 1 << 2,
      description: "El admin puede eliminar códigos de promociones",
    },
    LIST_PROMOTION_CODE: {
      value: 1 << 3,
      description: "El admin puede listar todos los códigos de promociones",
    },
  },
  code: {
    PUT_CODE: {
      value: 1 << 0,
      description: "El admin puede crear/editar códigos",
    },
    READ_CODE: {
      value: 1 << 1,
      description: "El admin puede ver códigos",
    },
    DELETE_CODE: {
      value: 1 << 2,
      description: "El admin puede eliminar códigos",
    },
    LIST_CODE: {
      value: 1 << 3,
      description: "El admin puede listar todos los códigos",
    },
  },
  pchujoyGame: {
    PUT_GAME: {
      value: 1 << 0,
      description: "El admin puede crear/editar juegos",
    },
    DELETE_GAME: {
      value: 1 << 1,
      description: "El admin puede eliminar juegos",
    },
  },
};
