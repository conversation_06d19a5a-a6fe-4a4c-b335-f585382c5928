import { common } from "@mainframe-peru/types";
import { Middleware } from "koa";
import { ZodError } from "zod";
import { AppError } from "../../error";
import { logger } from "../../log";

export const errorMiddleware: Middleware = async (ctx, next) => {
  try {
    await next();
  } catch (e) {
    if (e instanceof ZodError) {
      logger.warn("Failed zod validation", e);
      const body: common.HttpApiError = {
        code: "InvalidBody",
        message: "The given request body is not valid.",
        data: {
          issues: e.issues,
        },
      };
      ctx.response.status = 400;
      ctx.response.body = body;
    } else if (e instanceof AppError) {
      logger.logAppError(e);
      const body: common.HttpApiError = {
        code: e.code,
        message: e.message,
        data: e.responseBody,
      };
      if (e.allowRetry) ctx.response.set("retry", "allow");
      ctx.response.status = e.httpStatus.code;
      ctx.response.body = body;
    } else {
      logger.error(e instanceof Error ? e : "UnknownError", e);
      const body: common.HttpApiError = {
        code: "UnknownError",
        message: "An unknown error has ocurred.",
      };
      ctx.response.status = 500;
      ctx.response.body = body;
    }
  }
};
