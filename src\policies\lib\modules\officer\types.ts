// definimos las acciones de cada modulo
type OfficerModulePermissions =
  | "PUT_OFFICER"
  | "READ_OFFICER"
  | "DELETE_OFFICER"
  | "LIST_OFFICERS";

type OfficerAdminModulePermissions =
  | "PUT_ADMIN"
  | "READ_ADMIN"
  | "DELETE_ADMIN"
  | "LIST_ADMINS";

type OfficerUserModulePermissions =
  | "PUT_USER"
  | "READ_USER"
  | "DELETE_USER"
  | "LIST_USERS";

type OfficerInfluencerModulePermissions =
  | "PUT_INFLUENCER"
  | "READ_INFLUENCER"
  | "DELETE_INFLUENCER"
  | "LIST_INFLUENCERS";

type OfficerTransactionModulePermissions =
  | "PUT_TRANSACTION"
  | "READ_TRANSACTION"
  | "LIST_TRANSACTIONS";

type OfficerPaymentModulePermissions =
  | "PUT_PAYMENT"
  | "READ_PAYMENT"
  | "DELETE_PAYMENT"
  | "LIST_PAYMENTS";

type OfficerCardModulePermissions =
  | "PUT_CARD"
  | "READ_CARD"
  | "DELETE_CARD"
  | "LIST_CARDS";

type OfficerPlanModulePermissions =
  | "PUT_PLAN"
  | "READ_PLAN"
  | "DELETE_PLAN"
  | "LIST_PLANS";

type OfficerRecurrenceModulePermissions =
  | "PUT_RECURRENCE"
  | "READ_RECURRENCE"
  | "DELETE_RECURRENCE"
  | "LIST_RECURRENCES";

type OfficerEventModulePermissions =
  | "CREATE_EVENT"
  | "GET_EVENT"
  | "UPDATE_EVENT"
  | "DELETE_EVENT"
  | "LIST_EVENTS";

type OfficerComplaintModulePermissions = "LIST_ISSUES" | "PUT_ISSUE";

// mapeamos las acciones por cada modulo
export type OfficerPolicies = {
  officer: OfficerModulePermissions;
  card: OfficerCardModulePermissions;
  admin: OfficerAdminModulePermissions;
  user: OfficerUserModulePermissions;
  influencer: OfficerInfluencerModulePermissions;
  transaction: OfficerTransactionModulePermissions;
  payment: OfficerPaymentModulePermissions;
  plan: OfficerPlanModulePermissions;
  recurrence: OfficerRecurrenceModulePermissions;
  complaint: OfficerComplaintModulePermissions;
  event: OfficerEventModulePermissions;
};
