import { CreateUserRequest } from "@mainframe-peru/types/build/user";
import { Middleware } from "koa";
import { DefaultState, DefaultContext } from "koa";

export const sanitizeEmail: Middleware<DefaultState, DefaultContext> = async (
  ctx,
  next,
) => {
  const { body } = ctx.request as unknown as { body: CreateUserRequest };

  if (body.email) {
    body.email = body.email.trim().toLowerCase();
  }

  await next();
};
