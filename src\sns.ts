import { type PublishCommandInput } from "@aws-sdk/client-sns";

export type SNSMessage<T> = {
  topicArn: string;
  method?: "POST" | "GET" | "PUT";
  path: string;
  service: string;
  origin: string;
  payload: T;
};

export function getPublishInput<T>(args: SNSMessage<T>): PublishCommandInput {
  return {
    TargetArn: args.topicArn,
    Message: JSON.stringify(args.payload),
    MessageAttributes: {
      path: {
        DataType: "String",
        StringValue: args.path,
      },
      service: {
        DataType: "String",
        StringValue: args.service,
      },
      method: {
        DataType: "String",
        StringValue: args.method || "POST",
      },
      origin: {
        DataType: "String",
        StringValue: args.origin,
      },
    },
  };
}
