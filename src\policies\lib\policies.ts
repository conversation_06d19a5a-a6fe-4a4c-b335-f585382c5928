import {
  DeepPartial,
  PoliciesList,
  PolicyModules,
} from "../../types/main.types";
import { maskModulePolicies, unmaskModulesPolicies } from "../helpers";

export const Policies = {
  getUnmaskedDefaultPolicies<T>(
    policies: PolicyModules<T>,
  ): PolicyModules<T, boolean> {
    if (!policies) {
      throw new Error(
        "Error on getDefaultFormat method - 'policies' is undefined",
      );
    }

    let policyFormatted = {} as PolicyModules<T, boolean>;
    for (const module in policies) {
      // adding the module
      policyFormatted = {
        ...policyFormatted,
        [module]: {},
      };
      const permissions = policies[module];
      // iterating through the current permissions
      for (const permission in permissions) {
        const permissionDetail = policyFormatted[module] as Record<
          string,
          unknown
        >;
        permissionDetail[permission] = false;
      }
    }

    return policyFormatted;
  },

  unmask<T>(
    maskPolicy: Record<string, number>,
    currentPolicies: PolicyModules<T>,
  ): DeepPartial<PolicyModules<T, boolean>> {
    if (!maskPolicy) {
      throw new Error("Error on unmask method - 'maskPolicy' is undefiend");
    }
    const unmaskedPolicy: DeepPartial<PolicyModules<T, boolean>> = {};
    for (const module in currentPolicies) {
      const policyUnmasked = unmaskModulesPolicies<T>(
        module,
        maskPolicy[module] || 0,
        currentPolicies,
      );

      if (!policyUnmasked) {
        continue;
      }

      unmaskedPolicy[module] = policyUnmasked;
    }
    return unmaskedPolicy;
  },

  mask<T>(
    policiesToMask: DeepPartial<PolicyModules<T, boolean>>,
    currentPolicies: PolicyModules<T>,
  ): PolicyModules<T, number> {
    const policiesMasked = {} as PolicyModules<T, number>;
    for (const module in policiesToMask) {
      const maskedValue = maskModulePolicies<T>(
        module,
        policiesToMask,
        currentPolicies,
      );

      if (!maskedValue) {
        continue;
      }

      policiesMasked[module] = maskedValue;
    }

    return policiesMasked;
  },
  format: {
    toArray<T>(
      policyModulesObject: PolicyModules<T, boolean>,
    ): PoliciesList<{ name: keyof T; value: boolean }>[] {
      const policyModulesEntries = Object.entries(policyModulesObject);
      return policyModulesEntries.map(([key, value]) => {
        return {
          name: key,
          permissions: Object.entries(value as Record<string, boolean>).map(
            ([key, dataValue]) => ({
              name: key as keyof T,
              value: dataValue,
            }),
          ),
        };
      });
    },

    toObject<T>(
      policiesListArray: PoliciesList<{ name: string; value: boolean }>[],
    ): PolicyModules<T, boolean> {
      // building the policy modules object
      return policiesListArray.reduce(
        (prev, currentModule) => {
          const { permissions, name } = currentModule;
          // building the permissions object per module
          const permissionsFormatted = permissions.reduce(
            (prev, currentPermission) => {
              const { name, value } = currentPermission;
              return { ...prev, [name]: value };
            },
            {} as Record<string, boolean>,
          );
          return {
            ...prev,
            [name]: {
              ...permissionsFormatted,
            },
          };
        },
        {} as PolicyModules<T, boolean>,
      );
    },
  },
};
