import {
  APIGatewayProxyResultV2,
  <PERSON><PERSON>,
  SQSBatchItemFailure,
  SQSHandler,
  SQSRecord,
} from "aws-lambda";
import { logger } from "./log";

export type SQSSingleEvent = {
  // Necessary for serverless express to work
  Records: [SQSRecord];
};
export type SQSKoaEventHandler = Handler<
  SQSSingleEvent,
  APIGatewayProxyResultV2
>;

export const eventSource = {
  getRequest: ({ event }: { event: SQSSingleEvent }) => {
    const { method, path, origin } = event.Records[0].messageAttributes;
    return {
      method: method.stringValue,
      path: path.stringValue,
      headers: {
        "content-type": "application/json",
        origin: origin.stringValue,
        "retry-count": event.Records[0].attributes.ApproximateReceiveCount,
      },
      body: event.Records[0].body,
    };
  },
  getResponse: (proxyRes: APIGatewayProxyResultV2) => proxyRes,
};

export function buildSQSHandler(
  singleRecordHandler: SQSKoaEventHandler,
): SQSHandler {
  const handler: SQSHandler = async (event, context) => {
    logger.debug("Received event", { event });
    const failures: SQSBatchItemFailure[] = [];
    const iterable = event.Records.map((r) =>
      singleRecordHandler({ Records: [r] }, context, () => undefined),
    );
    const results = await Promise.allSettled(iterable);
    for (let i = 0; i < results.length; i++) {
      const r = results[i];

      // Error was not catch by the errorMiddleware
      if (r.status === "rejected") {
        if (r.reason instanceof Error) {
          logger.error(r.reason);
        } else {
          logger.error("Handler rejected record", { reason: r.reason });
        }
      }
      if (
        r.status === "rejected" ||
        (r.value instanceof Object && r.value.headers?.["retry"] === "allow")
      ) {
        failures.push({ itemIdentifier: event.Records[i].messageId });
      }
    }
    logger.debug("Returning failures", { failures });
    return {
      batchItemFailures: failures,
    };
  };
  return handler;
}
