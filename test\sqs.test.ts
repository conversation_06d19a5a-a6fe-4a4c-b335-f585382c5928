import { APIGatewayProxyResultV2 } from "aws-lambda";
import { buildSQSHandler, eventSource } from "../src/sqs";

describe("SQS", () => {
  test("return object on serverless express mapper", function () {
    const mappedRequest = eventSource.getRequest({
      event: {
        Records: [
          {
            attributes: {
              ApproximateReceiveCount: "1",
              SentTimestamp: "",
              SenderId: "",
              ApproximateFirstReceiveTimestamp: "",
            },
            awsRegion: "",
            body: "test json",
            eventSource: "",
            eventSourceARN: "",
            md5OfBody: "",
            messageAttributes: {
              method: {
                dataType: "String",
                stringValue: "POST",
              },
              path: {
                dataType: "String",
                stringValue: "/test",
              },
              origin: {
                dataType: "String",
                stringValue: "test.com",
              },
            },
            messageId: "",
            receiptHandle: "",
          },
        ],
      },
    });
    expect(mappedRequest).toEqual({
      method: "POST",
      path: "/test",
      headers: {
        "content-type": "application/json",
        origin: "test.com",
        "retry-count": "1",
      },
      body: "test json",
    });
    const proxyRes: APIGatewayProxyResultV2 = {
      statusCode: 200,
    };
    expect(eventSource.getResponse(proxyRes)).toEqual(proxyRes);
  });

  test("handle multiple events", async () => {
    const errorMessageId = crypto.randomUUID();
    const handler = buildSQSHandler(async (event) => {
      if (event.Records[0].messageId === errorMessageId) {
        return { statusCode: 400, headers: { retry: "allow" } };
      } else {
        return { statusCode: 200 };
      }
    });
    const result = await handler(
      {
        Records: [
          {
            messageId: errorMessageId,
            receiptHandle: "AQEBjr5k0kh1Smzqs1g==",
            body: JSON.stringify({}),
            attributes: {
              ApproximateReceiveCount: "1",
              AWSTraceHeader: "Root=1-6777a72cc;Sampled=1;Lineage=2:8b1ac1fe:0",
              SentTimestamp: "1735894823980",
              SenderId: "AROA47CR256MFHU73FIPT:core-backend-sqs",
              ApproximateFirstReceiveTimestamp: "1735894825041",
            },
            messageAttributes: {
              path: {
                stringValue: "/invoice/create",
                stringListValues: [],
                binaryListValues: [],
                dataType: "String",
              },
              method: {
                stringValue: "POST",
                stringListValues: [],
                binaryListValues: [],
                dataType: "String",
              },
              origin: {
                stringValue: "core-backend",
                stringListValues: [],
                binaryListValues: [],
                dataType: "String",
              },
            },
            md5OfBody: "0d5acfb4b0c779e9f889d3cf2572069d",
            eventSource: "aws:sqs",
            eventSourceARN:
              "arn:aws:sqs:us-east-1:************:core-backend-queue",
            awsRegion: "us-east-1",
          },
          {
            messageId: "b9a47352-207d-4bb9-9b43-19646753d365",
            receiptHandle: "AQEBjr5k0kh1Smzqs1g==",
            body: JSON.stringify({}),
            attributes: {
              ApproximateReceiveCount: "1",
              AWSTraceHeader: "Root=1-6777a72cc;Sampled=1;Lineage=2:8b1ac1fe:0",
              SentTimestamp: "1735894823980",
              SenderId: "AROA47CR256MFHU73FIPT:core-backend-sqs",
              ApproximateFirstReceiveTimestamp: "1735894825041",
            },
            messageAttributes: {
              path: {
                stringValue: "/recurrence/cancellation",
                stringListValues: [],
                binaryListValues: [],
                dataType: "String",
              },
              method: {
                stringValue: "POST",
                stringListValues: [],
                binaryListValues: [],
                dataType: "String",
              },
              origin: {
                stringValue: "core-backend",
                stringListValues: [],
                binaryListValues: [],
                dataType: "String",
              },
            },
            md5OfBody: "0d5acfb4b0c779e9f889d3cf2572069d",
            eventSource: "aws:sqs",
            eventSourceARN:
              "arn:aws:sqs:us-east-1:************:core-backend-queue",
            awsRegion: "us-east-1",
          },
        ],
      },
      {
        awsRequestId: crypto.randomUUID(),
        callbackWaitsForEmptyEventLoop: false,
        functionName: "core-backend-sqs",
        functionVersion: "187",
        getRemainingTimeInMillis: () => 0,
        invokedFunctionArn:
          "arn:aws:sqs:us-east-1:************:core-backend-queue",
        logGroupName: "core-backend-sqs",
        logStreamName: crypto.randomUUID(),
        memoryLimitInMB: "500",
        succeed: () => undefined,
        done: () => undefined,
        fail: () => undefined,
      },
      () => undefined,
    );

    expect(result).toBeInstanceOf(Object);
    if (result instanceof Object) {
      expect(result.batchItemFailures.length).toEqual(1);
      expect(result.batchItemFailures[0].itemIdentifier).toEqual(
        errorMessageId,
      );
    }
  });
});
