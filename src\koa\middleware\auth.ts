import { common } from "@mainframe-peru/types";
import { Middleware } from "koa";
import { AppError } from "../../error";
import {
  Auth,
  getSessionCookieValue,
  verifyClientJWT,
  getClientTypeByIssuer,
  getClientType,
  getSessionFromAuthorization,
} from "../../jwt";
import { PermissionDetails } from "../../types/main.types";
import {
  clientPoliciesConstant,
  ClientPolicies,
  ClientPoliciesIntersection,
  PermissionsNeededByClient,
} from "../../policies/lib/modules/common";

export type AuthorizedContextState<T extends Auth> = {
  auth: T;
};

export function getClientAuthMiddleware(
  officerPublicKey: string,
  adminPublicKey: string,
  userPublicKey: string,
): Middleware {
  const authMiddleware: Middleware<AuthorizedContextState<Auth>> = async (
    ctx,
    next,
  ) => {
    const cookie = ctx.req.headers.cookie;
    const auth = ctx.req.headers.authorization;
    let jwt;
    if (cookie) {
      jwt = getSessionCookieValue(cookie);
    }
    if (auth && !jwt) {
      jwt = getSessionFromAuthorization(auth);
    }
    if (!jwt) {
      throw new AppError({
        code: "BadAuthorization",
        message: "No cookies where set.",
        statusCode: "UNAUTHORIZED",
        logLevel: "INFO",
      });
    }
    const clientType = getClientType(jwt);
    ctx.state.auth = await verifyClientJWT(
      jwt,
      clientType == "officer"
        ? officerPublicKey
        : clientType == "admin"
          ? adminPublicKey
          : userPublicKey,
    );
    await next();
  };
  return authMiddleware;
}

export function getClientPoliciesValidationMiddleware(
  permissionsNeeded: PermissionsNeededByClient,
): Middleware {
  const middleware: Middleware<AuthorizedContextState<Auth>> = async (
    ctx,
    next,
  ) => {
    const client = ctx.state.auth;
    if (!client) {
      throw new AppError({
        code: "BadAuthorization",
        message: "No valid session was found.",
        statusCode: "FORBIDDEN",
        logLevel: "INFO",
      });
    }

    const clientType = getClientTypeByIssuer(
      client.iss as `mainframe:${common.ClientType}`,
    );
    const policiesBitmasks = client.policies as Record<string, number>;
    const clientPermissions =
      permissionsNeeded[clientType as keyof ClientPolicies];
    if (!clientPermissions) {
      throw new AppError({
        code: "BadAuthorization",
        message: "No valid session was found.",
        statusCode: "FORBIDDEN",
        logLevel: "INFO",
      });
    }

    // Getting set of policies in the *const* list, by module for the client (ex. for officer would be officerPolicies const)
    const clientPoliciesConst =
      clientPoliciesConstant[clientType as keyof typeof clientPoliciesConstant];
    // Traversing the modules in the required permissions list
    for (const module in clientPermissions) {
      // Getting the module *const* content (ex. getting officerPolicies's transaction module)
      const modulePoliciesConst =
        clientPoliciesConst[module as keyof typeof clientPoliciesConst];
      // Traversing the permissions in the required permissions list
      for (const requiredPermission of clientPermissions[
        module as keyof ClientPoliciesIntersection
      ] as []) {
        // Getting the action *const* content (ex. getting transaction's PUT_TRANSACTION policy)
        const actionDetails = modulePoliciesConst[
          requiredPermission as keyof typeof modulePoliciesConst
        ] as PermissionDetails;
        if (!(policiesBitmasks[module] & actionDetails.value)) {
          throw new AppError({
            code: "MissingRole",
            message: `The user does not have the role ${requiredPermission}.`,
            logLevel: "INFO",
          });
        }
      }
    }
    await next();
  };
  return middleware;
}
