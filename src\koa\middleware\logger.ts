import { Middleware } from "koa";
import { logger } from "../../log";

export const loggerMiddleware: Middleware = async (ctx, next) => {
  // Assume koa body-parse is being used
  const { body } = ctx.request as unknown as { body: unknown };
  logger.info("Got request.", {
    request: ctx.request,
    query: ctx.request.query,
    body: ctx.request.path.includes("login") ? undefined : body,
  });
  await next();
};
