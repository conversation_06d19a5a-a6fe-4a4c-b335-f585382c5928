export type PermissionDetails = { value: number; description: string };

export type ModuleDetails<K, V> = Record<
  Extract<K, string | number | symbol>,
  V
>;

type PolicyValue<U> = U extends PermissionDetails
  ? PermissionDetails
  : U extends boolean
    ? boolean
    : unknown;

export type PolicyModules<T, U = PermissionDetails> = {
  [K in keyof T]: U extends number
    ? number
    : ModuleDetails<T[K], PolicyValue<U>>;
};

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type PolicyList<U> = {
  moduleName: string;
  permissions: U;
};

export type PoliciesList<U> = {
  name: string;
  permissions: U[];
};
