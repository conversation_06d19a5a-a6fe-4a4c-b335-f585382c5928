import { generateKeyPairSync } from "node:crypto";
import {
  App<PERSON><PERSON><PERSON>,
  Auth,
  AuthOfficer,
  createJWT,
  verifyClientJWT,
} from "../../src";

describe("Admin JWT", () => {
  const { publicKey, privateKey } = generateKeyPairSync("rsa", {
    modulusLength: 2048,
    publicKeyEncoding: {
      type: "spki",
      format: "pem",
    },
    privateKeyEncoding: {
      type: "pkcs8",
      format: "pem",
    },
  });
  let jwt = "";

  test("create a admin JWT", async () => {
    const admin: Omit<AuthOfficer, "iss" | "exp"> = {
      id: 1,
      email: "<EMAIL>",
      firstName: "User",
      lastName: "too",
      policies: {
        officer: 3,
        transaction: 1,
      },
    };
    jwt = await createJWT(
      "officer",
      admin,
      privateKey,
      Math.floor(Date.now() / 1000) + 10,
    );

    const [, body] = jwt.split(".");
    const decoded = JSON.parse(Buffer.from(body, "base64").toString("utf-8"));
    expect(decoded).toEqual(expect.objectContaining(admin));
  });

  test("verify a valid admin JWT", async () => {
    const admin = await verifyClientJWT(jwt, publicKey);

    expect(admin).toEqual(
      expect.objectContaining({
        email: "<EMAIL>",
        firstName: "User",
        lastName: "too",
        policies: {
          officer: 3,
          transaction: 1,
        },
      }),
    );
  });

  test("verify an invalid admin JWT", async () => {
    const [header, , signature] = jwt.split(".");

    const body: Omit<Auth, "iss" | "exp"> = {
      id: 1,
      email: "<EMAIL>",
      firstName: "me",
      lastName: "too",
      policies: {
        officer: 3,
        transaction: 1,
      },
    };
    const base64Body = Buffer.from(JSON.stringify(body)).toString("base64");
    let error: AppError | undefined = undefined;
    try {
      await verifyClientJWT(
        [header, base64Body, signature].join("."),
        publicKey,
      );
    } catch (e) {
      error = e as AppError;
    }

    expect(error).toBeInstanceOf(AppError);
    expect(error?.code).toEqual("InvalidJWT");
    expect(error?.message).toEqual("Failed JWT validation.");
  });
});
