import { PolicyModules } from "../../../../types/main.types";
import { OfficerPolicies } from "./types";

// Define the constant
export const officerPoliciesConstant: PolicyModules<OfficerPolicies> = {
  officer: {
    PUT_OFFICER: {
      value: 1 << 0,
      description: "El officer puede crear/editar perfiles de officers",
    },
    READ_OFFICER: {
      value: 1 << 1,
      description: "El officer puede ver perfiles de officers",
    },
    DELETE_OFFICER: {
      value: 1 << 2,
      description: "El officer puede eliminar officers",
    },
    LIST_OFFICERS: {
      value: 1 << 3,
      description: "El officer puede listar todos los officers",
    },
  },
  admin: {
    PUT_ADMIN: {
      value: 1 << 0,
      description: "El officer puede crear/editar perfiles de admin",
    },
    READ_ADMIN: {
      value: 1 << 1,
      description: "El officer puede ver perfiles de admin",
    },
    DELETE_ADMIN: {
      value: 1 << 2,
      description: "El officer puede eliminar admins",
    },
    LIST_ADMINS: {
      value: 1 << 3,
      description: "El officer puede listar todos los admins",
    },
  },
  user: {
    PUT_USER: {
      value: 1 << 0,
      description: "El officer puede crear/editar perfiles de usuario",
    },
    READ_USER: {
      value: 1 << 1,
      description: "El officer puede ver perfiles de usuario",
    },
    DELETE_USER: {
      value: 1 << 2,
      description: "El officer puede eliminar usuarios",
    },
    LIST_USERS: {
      value: 1 << 3,
      description: "El officer puede listar todos los usuarios",
    },
  },
  influencer: {
    PUT_INFLUENCER: {
      value: 1 << 0,
      description: "El officer puede crear/editar perfiles de influencer",
    },
    READ_INFLUENCER: {
      value: 1 << 1,
      description: "El officer puede ver perfiles de influencers",
    },
    DELETE_INFLUENCER: {
      value: 1 << 2,
      description: "El officer puede eliminar influencers",
    },
    LIST_INFLUENCERS: {
      value: 1 << 3,
      description: "El officer puede listar todos los influencers",
    },
  },
  transaction: {
    PUT_TRANSACTION: {
      value: 1 << 0,
      description: "El officer puede crear/editar transacciones",
    },
    READ_TRANSACTION: {
      value: 1 << 1,
      description: "El officer puede consultar una transacción",
    },
    LIST_TRANSACTIONS: {
      value: 1 << 2,
      description: "El officer puede listar transacciones",
    },
  },
  payment: {
    PUT_PAYMENT: {
      value: 1 << 0,
      description: "El officer puede crear/editar perfiles de influencer",
    },
    READ_PAYMENT: {
      value: 1 << 1,
      description: "El officer puede ver perfiles de influencers",
    },
    DELETE_PAYMENT: {
      value: 1 << 2,
      description: "El officer puede eliminar influencers",
    },
    LIST_PAYMENTS: {
      value: 1 << 3,
      description: "El officer puede listar todos los influencers",
    },
  },
  card: {
    PUT_CARD: {
      value: 1 << 0,
      description: "El officer puede crear/editar tarjetas",
    },
    READ_CARD: {
      value: 1 << 1,
      description: "El officer puede ver tarjetas",
    },
    DELETE_CARD: {
      value: 1 << 2,
      description: "El officer puede eliminar tarjetas",
    },
    LIST_CARDS: {
      value: 1 << 3,
      description: "El officer puede listar todos los tarjetas de un usuario",
    },
  },
  plan: {
    PUT_PLAN: {
      value: 1 << 0,
      description: "El officer puede crear/editar planes",
    },
    READ_PLAN: {
      value: 1 << 1,
      description: "El officer puede ver planes",
    },
    DELETE_PLAN: {
      value: 1 << 2,
      description: "El officer puede eliminar planes",
    },
    LIST_PLANS: {
      value: 1 << 3,
      description: "El officer puede listar todos los planes de un influencer",
    },
  },
  recurrence: {
    PUT_RECURRENCE: {
      value: 1 << 0,
      description: "El officer puede crear/editar recurrencias",
    },
    READ_RECURRENCE: {
      value: 1 << 1,
      description: "El officer puede ver recurrencias",
    },
    DELETE_RECURRENCE: {
      value: 1 << 2,
      description: "El officer puede eliminar recurrencias",
    },
    LIST_RECURRENCES: {
      value: 1 << 3,
      description:
        "El officer puede listar todos los recurrencias de un influencer",
    },
  },
  complaint: {
    LIST_ISSUES: {
      value: 1 << 0,
      description: "El officer puede ver las quejas",
    },
    PUT_ISSUE: {
      value: 1 << 1,
      description: "El officer puede resolver los reclamos y quejas",
    },
  },
  event: {
    CREATE_EVENT: {
      value: 1 << 0,
      description: "El admin puede crear eventos",
    },
    GET_EVENT: {
      value: 1 << 1,
      description: "El admin puede ver un evento",
    },
    UPDATE_EVENT: {
      value: 1 << 2,
      description: "El admin puede actualizar un evento",
    },
    DELETE_EVENT: {
      value: 1 << 3,
      description: "El admin puede eliminar un evento",
    },
    LIST_EVENTS: {
      value: 1 << 4,
      description: "El admin puede listar los eventos",
    },
  },
};
