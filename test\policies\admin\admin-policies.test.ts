import { adminPoliciesConstant, AdminPolicies, Policies } from "../../../src";
import { PolicyModules } from "../../../src/types/main.types";

describe("testing influencer module", () => {
  test("get default influencer policies on false", () => {
    const result: PolicyModules<AdminPolicies, boolean> = {
      admin: {
        PUT_ADMIN: false,
        DELETE_ADMIN: false,
        ELEVATE_ADMIN: false,
        LIST_ADMINS: false,
        READ_ADMIN: false,
        REPORT_ADMIN: false,
      },
      influencer: {
        READ_INFLUENCER: false,
        UPDATE_INFLUENCER: false,
      },
      user: {
        DELETE_USER: false,
        LIST_USERS: false,
        PUT_USER: false,
        READ_USER: false,
      },
      transaction: {
        LIST_TRANSACTIONS: false,
        PUT_TRANSACTION: false,
        READ_TRANSACTION: false,
      },
      card: {
        DELETE_CARD: false,
        LIST_CARDS: false,
        PUT_CARD: false,
        READ_CARD: false,
      },
      plan: {
        DELETE_PLAN: false,
        LIST_PLANS: false,
        PUT_PLAN: false,
        READ_PLAN: false,
      },
      recurrence: {
        DELETE_RECURRENCE: false,
        LIST_RECURRENCES: false,
        PUT_RECURRENCE: false,
        READ_RECURRENCE: false,
      },
      invoice: {
        PUT_INVOICE: false,
        SEND_INVOICE: false,
      },
      complaint: {
        LIST_ISSUES: false,
        PUT_ISSUE: false,
      },
      paymentProviderEvent: {
        GET_EVENT: false,
      },
      event: {
        CREATE_EVENT: false,
        DELETE_EVENT: false,
        UPDATE_EVENT: false,
        GET_EVENT: false,
        LIST_EVENTS: false,
      },
      business: {
        DELETE_BUSINESS: false,
        LIST_BUSINESS: false,
        PUT_BUSINESS: false,
        READ_BUSINESS: false,
      },
      businessPromotion: {
        DELETE_PROMOTION: false,
        LIST_PROMOTION: false,
        PUT_PROMOTION: false,
        READ_PROMOTION: false,
      },
      businessPromotionCode: {
        DELETE_PROMOTION_CODE: false,
        LIST_PROMOTION_CODE: false,
        PUT_PROMOTION_CODE: false,
        READ_PROMOTION_CODE: false,
      },
      code: {
        DELETE_CODE: false,
        LIST_CODE: false,
        PUT_CODE: false,
        READ_CODE: false,
      },
      pchujoyGame: {
        DELETE_GAME: false,
        PUT_GAME: false,
      },
    };

    const defaultInfluencerPolicies = Policies.getUnmaskedDefaultPolicies(
      adminPoliciesConstant,
    );
    expect(defaultInfluencerPolicies).toEqual(result);
  });

  test("mask influencer policies", () => {
    const policiesToMask = {
      admin: {
        PUT_ADMIN: false,
        DELETE_ADMIN: true,
        ELEVATE_ADMIN: false,
        LIST_ADMINS: false,
        READ_ADMIN: false,
        REPORT_ADMIN: false,
      },
      influencer: {
        READ_INFLUENCER: false,
      },
      user: {
        DELETE_USER: false,
        LIST_USERS: false,
        PUT_USER: false,
        READ_USER: false,
      },
      transaction: {
        PUT_TRANSACTION: false,
        LIST_TRANSACTIONS: true,
        READ_TRANSACTION: false,
      },
    };

    const result = {
      admin: 4,
      transaction: 2,
    };

    const influencerPoliciesMasked = Policies.mask(
      policiesToMask,
      adminPoliciesConstant,
    );
    expect(influencerPoliciesMasked).toEqual(result);
  });

  test("unmask influencer policies", () => {
    const result = {
      admin: {
        PUT_ADMIN: false,
        DELETE_ADMIN: true,
        ELEVATE_ADMIN: false,
        LIST_ADMINS: false,
        READ_ADMIN: false,
        REPORT_ADMIN: false,
      },
      transaction: {
        PUT_TRANSACTION: false,
        LIST_TRANSACTIONS: true,
        READ_TRANSACTION: false,
      },
      influencer: {
        READ_INFLUENCER: false,
        UPDATE_INFLUENCER: false,
      },
      user: {
        DELETE_USER: false,
        LIST_USERS: false,
        PUT_USER: false,
        READ_USER: false,
      },
      card: {
        DELETE_CARD: false,
        LIST_CARDS: false,
        PUT_CARD: false,
        READ_CARD: false,
      },
      plan: {
        DELETE_PLAN: false,
        LIST_PLANS: false,
        PUT_PLAN: false,
        READ_PLAN: false,
      },
      recurrence: {
        DELETE_RECURRENCE: false,
        LIST_RECURRENCES: false,
        PUT_RECURRENCE: false,
        READ_RECURRENCE: false,
      },
      invoice: {
        PUT_INVOICE: false,
        SEND_INVOICE: false,
      },
      complaint: {
        LIST_ISSUES: false,
        PUT_ISSUE: false,
      },
      paymentProviderEvent: {
        GET_EVENT: false,
      },
      event: {
        CREATE_EVENT: false,
        DELETE_EVENT: false,
        UPDATE_EVENT: false,
        GET_EVENT: false,
        LIST_EVENTS: false,
      },
      business: {
        DELETE_BUSINESS: false,
        LIST_BUSINESS: false,
        PUT_BUSINESS: false,
        READ_BUSINESS: false,
      },
      businessPromotion: {
        DELETE_PROMOTION: false,
        LIST_PROMOTION: false,
        PUT_PROMOTION: false,
        READ_PROMOTION: false,
      },
      businessPromotionCode: {
        DELETE_PROMOTION_CODE: false,
        LIST_PROMOTION_CODE: false,
        PUT_PROMOTION_CODE: false,
        READ_PROMOTION_CODE: false,
      },
      code: {
        DELETE_CODE: false,
        LIST_CODE: false,
        PUT_CODE: false,
        READ_CODE: false,
      },
      pchujoyGame: {
        DELETE_GAME: false,
        PUT_GAME: false,
      },
    };

    const influencerPoliciesMasked = Policies.unmask(
      {
        admin: 4,
        transaction: 2,
      },
      adminPoliciesConstant,
    );
    expect(influencerPoliciesMasked).toEqual(result);
  });
});
