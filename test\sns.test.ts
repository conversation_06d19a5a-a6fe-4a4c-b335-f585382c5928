import { getPublishInput } from "../src/sns";

describe("SQS", () => {
  test("return object on serverless express mapper", function () {
    const input = getPublishInput({
      topicArn: "topicArn",
      origin: "test.com",
      path: "/test",
      payload: "test json",
      service: "core",
      method: "POST",
    });
    expect(input).toEqual({
      TargetArn: "topicArn",
      Message: JSON.stringify("test json"),
      MessageAttributes: {
        path: {
          DataType: "String",
          StringValue: "/test",
        },
        service: {
          DataType: "String",
          StringValue: "core",
        },
        method: {
          DataType: "String",
          StringValue: "POST",
        },
        origin: {
          DataType: "String",
          StringValue: "test.com",
        },
      },
    });
  });
});
