type AdminModulePermissions =
  | "PUT_ADMIN"
  | "READ_ADMIN"
  | "DELETE_ADMIN"
  | "LIST_ADMINS"
  | "ELEVATE_ADMIN"
  | "REPORT_ADMIN";

type AdminUserModulePermissions =
  | "PUT_USER"
  | "READ_USER"
  | "DELETE_USER"
  | "LIST_USERS";

type AdminInfluencerModulePermissions = "READ_INFLUENCER" | "UPDATE_INFLUENCER";

type AdminTransactionModulePermissions =
  | "PUT_TRANSACTION"
  | "READ_TRANSACTION"
  | "LIST_TRANSACTIONS";

type AdminCardModulePermissions =
  | "PUT_CARD"
  | "READ_CARD"
  | "DELETE_CARD"
  | "LIST_CARDS";

type AdminPlanModulePermissions =
  | "PUT_PLAN"
  | "READ_PLAN"
  | "DELETE_PLAN"
  | "LIST_PLANS";

type AdminRecurrenceModulePermissions =
  | "PUT_RECURRENCE"
  | "READ_RECURRENCE"
  | "DELETE_RECURRENCE"
  | "LIST_RECURRENCES";

type AdminEventModulePermissions =
  | "CREATE_EVENT"
  | "GET_EVENT"
  | "UPDATE_EVENT"
  | "DELETE_EVENT"
  | "LIST_EVENTS";

type AdminComplaintModulePermissions = "LIST_ISSUES" | "PUT_ISSUE";
type AdminInvoiceModulePermissions = "PUT_INVOICE" | "SEND_INVOICE";
type AdminPaymentProviderEventPermissions = "GET_EVENT";

type AdminBusinessModulePermissions =
  | "PUT_BUSINESS"
  | "READ_BUSINESS"
  | "DELETE_BUSINESS"
  | "LIST_BUSINESS";

type AdminBusinessPromotionModulePermissions =
  | "PUT_PROMOTION"
  | "READ_PROMOTION"
  | "DELETE_PROMOTION"
  | "LIST_PROMOTION";

type AdminBusinessPromotionCodeModulePermissions =
  | "PUT_PROMOTION_CODE"
  | "READ_PROMOTION_CODE"
  | "DELETE_PROMOTION_CODE"
  | "LIST_PROMOTION_CODE";

type AdminPchujoyGameModulePermissions = "PUT_GAME" | "DELETE_GAME";

type AdminCodeModulePermissions =
  | "PUT_CODE"
  | "READ_CODE"
  | "DELETE_CODE"
  | "LIST_CODE";

export type AdminPolicies = {
  card: AdminCardModulePermissions;
  admin: AdminModulePermissions;
  user: AdminUserModulePermissions;
  influencer: AdminInfluencerModulePermissions;
  transaction: AdminTransactionModulePermissions;
  plan: AdminPlanModulePermissions;
  recurrence: AdminRecurrenceModulePermissions;
  invoice: AdminInvoiceModulePermissions;
  complaint: AdminComplaintModulePermissions;
  paymentProviderEvent: AdminPaymentProviderEventPermissions;
  event: AdminEventModulePermissions;
  business: AdminBusinessModulePermissions;
  businessPromotion: AdminBusinessPromotionModulePermissions;
  businessPromotionCode: AdminBusinessPromotionCodeModulePermissions;
  code: AdminCodeModulePermissions;
  pchujoyGame: AdminPchujoyGameModulePermissions;
};
