import {
  SendMessageCommand,
  SendMessageCommandOutput,
  SQSClient,
} from "@aws-sdk/client-sqs";
import { ActivityLogRequest } from "@mainframe-peru/types/build/common";

export const activityLogQueue = {
  async sendActivityLogEvent(
    client: SQSClient,
    request: ActivityLogRequest,
  ): Promise<SendMessageCommandOutput> {
    const command = new SendMessageCommand({
      QueueUrl: process.env.ACTIVITY_LOG_QUEUE_URL,
      MessageBody: JSON.stringify(request),
      MessageAttributes: {
        path: {
          DataType: "String",
          StringValue: "/",
        },
        method: {
          DataType: "String",
          StringValue: "POST",
        },
        origin: {
          DataType: "String",
          StringValue: "common-core",
        },
      },
    });
    return await client.send(command);
  },
};
