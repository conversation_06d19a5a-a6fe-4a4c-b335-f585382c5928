import { createMockContext } from "@shopify/jest-koa-mocks";
import { generateKeyPairSync } from "crypto";
import {
  AppError,
  Auth,
  createJWT,
  getClientAuthMiddleware,
  getClientPoliciesValidationMiddleware,
} from "../../../src";

describe("Officer JWT", () => {
  const { publicKey, privateKey } = generateKeyPairSync("rsa", {
    modulusLength: 4096,
    publicKeyEncoding: {
      type: "spki",
      format: "pem",
    },
    privateKeyEncoding: {
      type: "pkcs8",
      format: "pem",
    },
  });

  test("will verify a jwt token", async () => {
    const jwt = await createJWT(
      "officer",
      {
        id: 1,
        email: "<EMAIL>",
        firstName: "User",
        lastName: "too",
        policies: {
          officer: 3,
          transaction: 1,
        },
      },
      privateKey,
      Math.floor(Date.now() / 1000) + 10,
    );

    const ctx = createMockContext({
      headers: {
        cookie: `session=${jwt}`,
      },
    });
    const next = jest.fn();

    await getClientAuthMiddleware(publicKey, publicKey, publicKey)(ctx, next);

    expect(next).toHaveBeenCalled();
  });

  test("will verify a jwt token on a auth header", async () => {
    const jwt = await createJWT(
      "officer",
      {
        id: 1,
        email: "<EMAIL>",
        firstName: "User",
        lastName: "too",
        policies: {
          officer: 3,
          transaction: 1,
        },
      },
      privateKey,
      Math.floor(Date.now() / 1000) + 10,
    );

    const ctx = createMockContext({
      headers: {
        cookie: "session1=some-cookie",
        Authorization: `Bearer ${jwt}`,
      },
    });
    const next = jest.fn();

    await getClientAuthMiddleware(publicKey, publicKey, publicKey)(ctx, next);

    expect(next).toHaveBeenCalled();
  });

  test("will verify a invalid jwt token", async () => {
    const ctx = createMockContext({
      headers: {
        cookie: `session=test`,
      },
    });
    const next = jest.fn();

    try {
      await getClientAuthMiddleware(publicKey, publicKey, publicKey)(ctx, next);
    } catch (e) {
      expect(e).toBeInstanceOf(AppError);
    }

    expect(next).toHaveBeenCalledTimes(0);
  });

  test("will verify user roles", async () => {
    const auth: Auth = {
      id: 1,
      email: "<EMAIL>",
      firstName: "User",
      lastName: "too",
      policies: {
        officer: 3,
        transaction: 1,
        complaint: 1,
      },
      iss: "mainframe:officer",
      exp: 123,
    };
    const ctx = createMockContext({ state: { auth } });
    const next = jest.fn();

    await getClientPoliciesValidationMiddleware({
      officer: {
        officer: ["PUT_OFFICER", "READ_OFFICER"],
        complaint: ["LIST_ISSUES"],
      },
    })(ctx, next);

    expect(next).toHaveBeenCalled();
  });

  test("will fail with missing roles", async () => {
    const auth: Auth = {
      id: 1,
      email: "<EMAIL>",
      firstName: "User",
      lastName: "too",
      policies: {
        officer: 3,
        transaction: 1,
      },
      iss: "mainframe:officer",
      exp: 123,
    };
    const ctx = createMockContext({ state: { auth } });
    const next = jest.fn();

    try {
      await getClientPoliciesValidationMiddleware({
        officer: {
          officer: ["PUT_OFFICER", "LIST_OFFICERS"],
        },
      })(ctx, next);
    } catch (e) {
      const error = e as AppError;
      expect(error).toBeInstanceOf(AppError);
      expect(error.code).toEqual("MissingRole");
    }

    expect(next).toHaveBeenCalledTimes(0);
  });
});
