import { createMockContext } from "@shopify/jest-koa-mocks";
import { SQSClient } from "@aws-sdk/client-sqs";
import { sendActivityLogEventMiddleware } from "../../../src/koa/middleware/activityLog";
import { activityLogQueue } from "../../../src/koa/activityLogSQS";
import { AppError, Auth } from "../../../src";

// Mock the activityLogQueue module
jest.mock("../../../src/koa/activityLogSQS", () => ({
  activityLogQueue: {
    sendActivityLogEvent: jest.fn(),
  },
}));

const mockSendActivityLogEvent = activityLogQueue.sendActivityLogEvent as jest.MockedFunction<
  typeof activityLogQueue.sendActivityLogEvent
>;

describe("Activity Log Middleware", () => {
  let mockSQSClient: SQSClient;

  beforeEach(() => {
    jest.clearAllMocks();
    mockSQSClient = {} as SQSClient;
    mockSendActivityLogEvent.mockResolvedValue({
      MessageId: "test-message-id",
      MD5OfMessageBody: "test-md5",
      $metadata: {},
    });
  });

  const mockOfficerAuth: Auth = {
    id: 1,
    email: "<EMAIL>",
    firstName: "Officer",
    lastName: "Test",
    policies: {
      officer: 3,
      transaction: 1,
    },
    iss: "mainframe:officer",
    exp: Math.floor(Date.now() / 1000) + 3600,
  };

  const mockAdminAuth: Auth = {
    id: 2,
    influencerId: "test-influencer",
    email: "<EMAIL>",
    firstName: "Admin",
    lastName: "Test",
    policies: {
      admin: 3,
      user: 1,
    },
    iss: "mainframe:admin",
    exp: Math.floor(Date.now() / 1000) + 3600,
  };

  const mockUserAuth: Auth = {
    id: 3,
    influencerId: "test-influencer",
    email: "<EMAIL>",
    firstName: "User",
    lastName: "Test",
    policies: {
      user: 1,
    },
    iss: "mainframe:user",
    exp: Math.floor(Date.now() / 1000) + 3600,
  };

  /*test("should send activity log with SUCCESS status when next() succeeds for officer", async () => {
    const ctx = createMockContext({
      state: { auth: mockOfficerAuth },
    });
    const next = jest.fn().mockResolvedValue(undefined);

    const middleware = sendActivityLogEventMiddleware(mockSQSClient, "LOGIN");
    await middleware(ctx, next);

    expect(next).toHaveBeenCalledTimes(1);
    expect(mockSendActivityLogEvent).toHaveBeenCalledTimes(1);
    expect(mockSendActivityLogEvent).toHaveBeenCalledWith(mockSQSClient, {
      type: "LOGIN",
      clientType: "officer",
      clientId: 1,
      status: "SUCCESS",
      timestamp: expect.any(Date),
    });
  });

  test("should send activity log with SUCCESS status when next() succeeds for admin", async () => {
    const ctx = createMockContext({
      state: { auth: mockAdminAuth },
    });
    const next = jest.fn().mockResolvedValue(undefined);

    const middleware = sendActivityLogEventMiddleware(mockSQSClient, "LOGOUT");
    await middleware(ctx, next);

    expect(next).toHaveBeenCalledTimes(1);
    expect(mockSendActivityLogEvent).toHaveBeenCalledTimes(1);
    expect(mockSendActivityLogEvent).toHaveBeenCalledWith(mockSQSClient, {
      type: "LOGOUT",
      clientType: "admin",
      clientId: 2,
      status: "SUCCESS",
      timestamp: expect.any(Date),
    });
  });

  test("should send activity log with SUCCESS status when next() succeeds for user", async () => {
    const ctx = createMockContext({
      state: { auth: mockUserAuth },
    });
    const next = jest.fn().mockResolvedValue(undefined);

    const middleware = sendActivityLogEventMiddleware(mockSQSClient, "CREATE_ACCOUNT");
    await middleware(ctx, next);

    expect(next).toHaveBeenCalledTimes(1);
    expect(mockSendActivityLogEvent).toHaveBeenCalledTimes(1);
    expect(mockSendActivityLogEvent).toHaveBeenCalledWith(mockSQSClient, {
      type: "CREATE_ACCOUNT",
      clientType: "user",
      clientId: 3,
      status: "SUCCESS",
      timestamp: expect.any(Date),
    });
  });

  test("should send activity log with FAIL status when next() throws an error", async () => {
    const ctx = createMockContext({
      state: { auth: mockOfficerAuth },
    });
    const testError = new Error("Test error");
    const next = jest.fn().mockRejectedValue(testError);

    const middleware = sendActivityLogEventMiddleware(mockSQSClient, "LOGIN");
    
    // The middleware should not throw the error, it should catch it and log it
    await middleware(ctx, next);

    expect(next).toHaveBeenCalledTimes(1);
    expect(mockSendActivityLogEvent).toHaveBeenCalledTimes(1);
    expect(mockSendActivityLogEvent).toHaveBeenCalledWith(mockSQSClient, {
      type: "LOGIN",
      clientType: "officer",
      clientId: 1,
      status: "FAIL",
      timestamp: expect.any(Date),
    });
  });

  test("should throw AppError when no auth is present in context state", async () => {
    const ctx = createMockContext({
      state: {},
    });
    const next = jest.fn();

    const middleware = sendActivityLogEventMiddleware(mockSQSClient, "LOGIN");

    await expect(middleware(ctx, next)).rejects.toThrow(AppError);
    await expect(middleware(ctx, next)).rejects.toMatchObject({
      code: "BadAuthorization",
      message: "No valid session was found.",
    });

    expect(next).not.toHaveBeenCalled();
    expect(mockSendActivityLogEvent).not.toHaveBeenCalled();
  });

  test("should throw AppError when auth is null", async () => {
    const ctx = createMockContext({
      state: { auth: null },
    });
    const next = jest.fn();

    const middleware = sendActivityLogEventMiddleware(mockSQSClient, "LOGIN");

    await expect(middleware(ctx, next)).rejects.toThrow(AppError);
    await expect(middleware(ctx, next)).rejects.toMatchObject({
      code: "BadAuthorization",
      message: "No valid session was found.",
    });

    expect(next).not.toHaveBeenCalled();
    expect(mockSendActivityLogEvent).not.toHaveBeenCalled();
  });

  test("should handle different activity log event types", async () => {
    const ctx = createMockContext({
      state: { auth: mockOfficerAuth },
    });
    const next = jest.fn().mockResolvedValue(undefined);

    // Test different event types
    const eventTypes = ["LOGIN", "LOGOUT", "CREATE_ACCOUNT", "UPDATE_PASSWORD"] as const;

    for (const eventType of eventTypes) {
      const middleware = sendActivityLogEventMiddleware(mockSQSClient, eventType);
      await middleware(ctx, next);

      expect(mockSendActivityLogEvent).toHaveBeenCalledWith(mockSQSClient, {
        type: eventType,
        clientType: "officer",
        clientId: 1,
        status: "SUCCESS",
        timestamp: expect.any(Date),
      });
    }

    expect(mockSendActivityLogEvent).toHaveBeenCalledTimes(eventTypes.length);
  });*/

  test("should handle SQS send failure gracefully", async () => {
    const ctx = createMockContext({
      state: { auth: mockOfficerAuth },
    });
    const next = jest.fn().mockResolvedValue(undefined);
    
    // Mock SQS send to fail
    mockSendActivityLogEvent.mockRejectedValue(new Error("SQS Error"));

    const middleware = sendActivityLogEventMiddleware(mockSQSClient, "LOGIN");
    
    // The middleware should not throw even if SQS fails
    await expect(middleware(ctx, next)).resolves.not.toThrow();

    expect(next).toHaveBeenCalledTimes(1);
    expect(mockSendActivityLogEvent).toHaveBeenCalledTimes(1);
  });
});
