import { createMockContext } from "@shopify/jest-koa-mocks";
import { SQSClient } from "@aws-sdk/client-sqs";
import { sendActivityLogEventMiddleware } from "../../../src/koa/middleware/activityLog";
import { activityLogQueue } from "../../../src/koa/activityLogSQS";
import { Auth } from "../../../src";

// Mock the activityLogQueue module
jest.mock("../../../src/koa/activityLogSQS", () => ({
  activityLogQueue: {
    sendActivityLogEvent: jest.fn(),
  },
}));

const mockSendActivityLogEvent =
  activityLogQueue.sendActivityLogEvent as jest.MockedFunction<
    typeof activityLogQueue.sendActivityLogEvent
  >;

describe("Activity Log Middleware", () => {
  let mockSQSClient: SQSClient;

  beforeEach(() => {
    jest.clearAllMocks();
    mockSQSClient = {} as SQSClient;
    mockSendActivityLogEvent.mockResolvedValue({
      MessageId: "test-message-id",
      MD5OfMessageBody: "test-md5",
      $metadata: {},
    });
  });

  const mockOfficerAuth: Auth = {
    id: 1,
    email: "<EMAIL>",
    firstName: "Officer",
    lastName: "Test",
    policies: {
      officer: 3,
      transaction: 1,
    },
    iss: "mainframe:officer",
    exp: Math.floor(Date.now() / 1000) + 3600,
  };

  const mockAdminAuth: Auth = {
    id: 2,
    influencerId: "test-influencer",
    email: "<EMAIL>",
    firstName: "Admin",
    lastName: "Test",
    policies: {
      admin: 3,
      user: 1,
    },
    iss: "mainframe:admin",
    exp: Math.floor(Date.now() / 1000) + 3600,
  };

  const mockUserAuth: Auth = {
    id: 3,
    influencerId: "test-influencer",
    email: "<EMAIL>",
    firstName: "User",
    lastName: "Test",
    policies: {
      user: 1,
    },
    iss: "mainframe:user",
    exp: Math.floor(Date.now() / 1000) + 3600,
  };

  test("should send activity log with SUCCESS status when next()", async () => {
    const ctx = createMockContext({
      state: { auth: mockUserAuth },
    });
    const next = jest.fn().mockResolvedValue(undefined);

    const middleware = sendActivityLogEventMiddleware(
      mockSQSClient,
      "CREATE_ACCOUNT",
    );
    await middleware(ctx, next);

    expect(next).toHaveBeenCalledTimes(1);
    expect(mockSendActivityLogEvent).toHaveBeenCalledTimes(1);
    expect(mockSendActivityLogEvent).toHaveBeenCalledWith(mockSQSClient, {
      type: "CREATE_ACCOUNT",
      clientType: "user",
      clientId: 3,
      status: "SUCCESS",
      influencerId: mockUserAuth.influencerId,
      request: {},
      timestamp: expect.any(Date),
    });
  });

  test("should send activity log with FAIL status when next() throws an error", async () => {
    const ctx = createMockContext({
      state: { auth: mockOfficerAuth },
    });
    const testError = new Error("Test error");
    const next = jest.fn().mockRejectedValue(testError);

    const middleware = sendActivityLogEventMiddleware(mockSQSClient, "LOGIN");

    // The middleware should not throw the error, it should catch it and log it
    await middleware(ctx, next);

    expect(next).toHaveBeenCalledTimes(1);
    expect(mockSendActivityLogEvent).toHaveBeenCalledTimes(1);
    expect(mockSendActivityLogEvent).toHaveBeenCalledWith(mockSQSClient, {
      type: "LOGIN",
      clientType: "officer",
      clientId: 1,
      status: "FAIL",
      influencerId: "mainframe",
      request: {},
      timestamp: expect.any(Date),
    });
  });
});
