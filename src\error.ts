import { LogLevel } from "./log";
import { StatusCodes } from "./statusCodes";

export type Info = Partial<{
  statusCode: keyof typeof StatusCodes;
  logLevel: LogLevel;
  code: string;
  message: string;
  data: unknown;
  responseBody: unknown;
  allowRetry: boolean;
}>;

export class AppError extends Error {
  httpStatus: {
    code: number;
    name: keyof typeof StatusCodes;
  };
  logLevel: LogLevel;
  code: string;
  message: string;
  data?: unknown;
  responseBody?: unknown;
  previous?: Error;
  allowRetry: boolean;

  constructor(info: Info, previous?: Error) {
    super(info.message);

    this.httpStatus = {
      code: StatusCodes[info.statusCode || "BAD_REQUEST"],
      name: info.statusCode || "BAD_REQUEST",
    };
    this.logLevel = info.logLevel || "ERROR";
    this.code = info.code || "UnknownError";
    this.message = info.message || "";
    this.data = info.data;
    this.responseBody = info.responseBody;
    this.allowRetry = !!info.allowRetry;
    this.previous = previous;
  }

  static fromError(error: Error | AppError): AppError {
    if (error instanceof AppError) {
      return error;
    }
    return new AppError(
      {
        message: error.message,
        code: "UnhandledError",
        logLevel: "ERROR",
        statusCode: "INTERNAL_SERVER_ERROR",
        data: { error },
      },
      error,
    );
  }
}
