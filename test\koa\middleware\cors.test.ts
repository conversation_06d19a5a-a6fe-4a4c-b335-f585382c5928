import { createMockContext } from "@shopify/jest-koa-mocks";
import { getCorsMiddleware } from "../../../src";

describe("Cors middleware", () => {
  test("will add a cors header", async () => {
    const ctx = createMockContext({
      headers: {
        origin: "https://admin.dev.pchujoy.app",
      },
    });
    const next = jest.fn();

    await getCorsMiddleware(["pchujoy.app"])(ctx, next);

    expect(next).toHaveBeenCalled();
    expect(ctx.response.headers["access-control-allow-origin"]).toBe(
      "https://pchujoy.app",
    );
    expect(ctx.response.headers["access-control-allow-credentials"]).toBe(
      "true",
    );
  });

  test("will not add a cors header if domain does not match", async () => {
    const ctx = createMockContext({
      headers: {
        origin: "https://admin.hack.phillip.app",
      },
    });
    const next = jest.fn();

    await getCorsMiddleware(["pchujoy.app"])(ctx, next);

    expect(next).toHaveBeenCalled();
    expect(ctx.response.headers["access-control-allow-origin"]).toBe(undefined);
    expect(ctx.response.headers["access-control-allow-credentials"]).toBe(
      undefined,
    );
  });

  test("will not add a cors header if origin is not set", async () => {
    const ctx = createMockContext();
    const next = jest.fn();

    await getCorsMiddleware(["pchujoy.app"])(ctx, next);

    expect(next).toHaveBeenCalled();
    expect(ctx.response.headers["access-control-allow-origin"]).toBe(undefined);
    expect(ctx.response.headers["access-control-allow-credentials"]).toBe(
      undefined,
    );
  });
});
