import {
  DeepPartial,
  ModuleDetails,
  PermissionDetails,
  PolicyModules,
} from "../../types/main.types";

export type UnmaskModule<T> = {
  module: keyof T;
  maskModulePolicyValue: number;
  currentPolicies: PolicyModules<T>;
};
export function unmaskModulesPolicies<T>(
  module: keyof T,
  maskModulePolicyValue: number,
  currentPolicies: PolicyModules<T>,
): ModuleDetails<string, boolean> | null {
  if (module === undefined) {
    return null;
  }

  const unmaskPermissionsDetail: ModuleDetails<string, boolean> = {};
  const maskedModulePermissions = currentPolicies[module];
  // iterating through all the masked permissions
  for (const permission in maskedModulePermissions) {
    const permissionDetails = currentPolicies[module][permission];
    // if any of the bits match then true
    unmaskPermissionsDetail[permission] = !!(
      maskModulePolicyValue & permissionDetails.value
    );
  }

  return unmaskPermissionsDetail;
}

export function maskModulePolicies<T>(
  module: keyof T,
  policiesToMask: DeepPartial<PolicyModules<T, boolean>>,
  currentPolicies: PolicyModules<T>,
): number | null {
  let permissionActive = 0;
  for (const permission in policiesToMask[module]) {
    const policyPermission = currentPolicies[module] as Record<
      string,
      PermissionDetails
    >;
    const currentPermission = (
      policiesToMask[module] as Record<string, boolean>
    )[permission];
    if (currentPermission) {
      permissionActive |= policyPermission[permission].value ?? 0;
    }
  }
  return permissionActive;
}
