import { BinaryPolicies } from "@mainframe-peru/types/build/common";
import {
  OfficerPolicies,
  officerPoliciesConstant,
  Policies,
} from "../../../src";
import { PolicyModules } from "../../../src/types/main.types";

describe("testing officer module", () => {
  test("get default officer policies on false", () => {
    const result: PolicyModules<OfficerPolicies, boolean> = {
      officer: {
        PUT_OFFICER: false,
        READ_OFFICER: false,
        DELETE_OFFICER: false,
        LIST_OFFICERS: false,
      },
      transaction: {
        LIST_TRANSACTIONS: false,
        PUT_TRANSACTION: false,
        READ_TRANSACTION: false,
      },
      influencer: {
        DELETE_INFLUENCER: false,
        LIST_INFLUENCERS: false,
        PUT_INFLUENCER: false,
        READ_INFLUENCER: false,
      },
      user: {
        DELETE_USER: false,
        LIST_USERS: false,
        PUT_USER: false,
        READ_USER: false,
      },
      admin: {
        PUT_ADMIN: false,
        READ_ADMIN: false,
        LIST_ADMINS: false,
        DELETE_ADMIN: false,
      },
      card: {
        DELETE_CARD: false,
        LIST_CARDS: false,
        PUT_CARD: false,
        READ_CARD: false,
      },
      payment: {
        DELETE_PAYMENT: false,
        LIST_PAYMENTS: false,
        PUT_PAYMENT: false,
        READ_PAYMENT: false,
      },
      plan: {
        DELETE_PLAN: false,
        LIST_PLANS: false,
        PUT_PLAN: false,
        READ_PLAN: false,
      },
      recurrence: {
        DELETE_RECURRENCE: false,
        LIST_RECURRENCES: false,
        PUT_RECURRENCE: false,
        READ_RECURRENCE: false,
      },
      complaint: {
        LIST_ISSUES: false,
        PUT_ISSUE: false,
      },
      event: {
        CREATE_EVENT: false,
        DELETE_EVENT: false,
        UPDATE_EVENT: false,
        GET_EVENT: false,
        LIST_EVENTS: false,
      },
    };

    const defaultOfficerPolicies = Policies.getUnmaskedDefaultPolicies(
      officerPoliciesConstant,
    );
    expect(defaultOfficerPolicies).toEqual(result);
  });

  test("mask officer policies", () => {
    const policiesToMask = {
      officer: {
        PUT_OFFICER: true,
        READ_OFFICER: false,
        DELETE_OFFICER: false,
        LIST_OFFICERS: false,
      },
      transaction: {
        PUT_TRANSACTION: false,
        LIST_TRANSACTIONS: true,
        READ_TRANSACTION: false,
      },
    };

    const result: BinaryPolicies = {
      officer: 1,
      transaction: 4,
    };

    const officerPoliciesMasked = Policies.mask(
      policiesToMask,
      officerPoliciesConstant,
    );
    expect(officerPoliciesMasked).toEqual(result);
  });

  test("unmask officer policies", () => {
    const policiesToUnmask: BinaryPolicies = {
      officer: 1,
      transaction: 4,
    };

    const result = {
      influencer: {
        DELETE_INFLUENCER: false,
        LIST_INFLUENCERS: false,
        PUT_INFLUENCER: false,
        READ_INFLUENCER: false,
      },
      user: {
        DELETE_USER: false,
        LIST_USERS: false,
        PUT_USER: false,
        READ_USER: false,
      },
      admin: {
        PUT_ADMIN: false,
        READ_ADMIN: false,
        LIST_ADMINS: false,
        DELETE_ADMIN: false,
      },
      card: {
        DELETE_CARD: false,
        LIST_CARDS: false,
        PUT_CARD: false,
        READ_CARD: false,
      },
      payment: {
        DELETE_PAYMENT: false,
        LIST_PAYMENTS: false,
        PUT_PAYMENT: false,
        READ_PAYMENT: false,
      },
      plan: {
        DELETE_PLAN: false,
        LIST_PLANS: false,
        PUT_PLAN: false,
        READ_PLAN: false,
      },
      recurrence: {
        DELETE_RECURRENCE: false,
        LIST_RECURRENCES: false,
        PUT_RECURRENCE: false,
        READ_RECURRENCE: false,
      },
      complaint: {
        LIST_ISSUES: false,
        PUT_ISSUE: false,
      },
      event: {
        CREATE_EVENT: false,
        DELETE_EVENT: false,
        UPDATE_EVENT: false,
        GET_EVENT: false,
        LIST_EVENTS: false,
      },
      officer: {
        PUT_OFFICER: true,
        READ_OFFICER: false,
        DELETE_OFFICER: false,
        LIST_OFFICERS: false,
      },
      transaction: {
        PUT_TRANSACTION: false,
        LIST_TRANSACTIONS: true,
        READ_TRANSACTION: false,
      },
    };

    const officerPoliciesUnmasked = Policies.unmask(
      policiesToUnmask,
      officerPoliciesConstant,
    );
    expect(officerPoliciesUnmasked).toEqual(result);
  });

  test("Format Unmasked Policies to array", () => {
    const policiesToFormat = {
      officer: { PUT_OFFICER: false, READ_OFFICER: false },
      transaction: { PUT_TRANSACTION: false },
    };

    const policiesFormatted = Policies.format.toArray(policiesToFormat);

    expect(policiesFormatted).toEqual([
      {
        name: "officer",
        permissions: [
          { name: "PUT_OFFICER", value: false },
          { name: "READ_OFFICER", value: false },
        ],
      },
      {
        name: "transaction",
        permissions: [{ name: "PUT_TRANSACTION", value: false }],
      },
    ]);
  });

  test("Format Unmasked Policies to object from array", () => {
    const policiesArray = [
      {
        name: "officer",
        permissions: [
          { name: "PUT_OFFICER", value: false },
          { name: "READ_OFFICER", value: false },
        ],
      },
      {
        name: "transaction",
        permissions: [{ name: "PUT_TRANSACTION", value: false }],
      },
    ];
    const policiesFormatted = Policies.format.toObject(policiesArray);

    expect(policiesFormatted).toEqual({
      officer: { PUT_OFFICER: false, READ_OFFICER: false },
      transaction: { PUT_TRANSACTION: false },
    });
  });
});
