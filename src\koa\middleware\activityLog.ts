import { common } from "@mainframe-peru/types";
import { Middleware } from "koa";
import { AppError } from "../../error";
import { activityLogQueue } from "../activityLogSQS";
import { ActivityLogEvent, ActivityLogRequest } from "@mainframe-peru/types/build/common";
import { AuthorizedContextState } from "./auth";
import { Auth, getClientTypeByIssuer } from "../../jwt";
import {
  SQSClient,
} from "@aws-sdk/client-sqs";

export function sendActivityLogEventMiddleware(SQSClient: SQSClient, type: ActivityLogEvent): Middleware {
  const activityLogMiddleware: Middleware<AuthorizedContextState<Auth>> = async (ctx, next) => {
    const client = ctx.state.auth;
    if (!client) {
      throw new AppError({
        code: "BadAuthorization",
        message: "No valid session was found.",
        statusCode: "FORBIDDEN",
        logLevel: "INFO",
      });
    }

    const clientType = getClientTypeByIssuer(
      client.iss as `mainframe:${common.ClientType}`,
    );

    const activityLog: ActivityLogRequest = {
      type,
      clientType,
      clientId: client.id,
      status: "SUCCESS",
      timestamp: new Date(),
    };
    console.log("1NO")
    try {
      console.log("2do")
      await next();
      console.log("3ro")
      await activityLogQueue.sendActivityLogEvent(SQSClient, activityLog);
      console.log("4to")
    } catch (e) {
      console.log("5to")
      await activityLogQueue.sendActivityLogEvent(SQSClient, {
        ...activityLog,
        status: "FAIL",
      });
      console.log("6to")
    }
  };
  return activityLogMiddleware;
}
