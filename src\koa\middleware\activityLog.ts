import { common } from "@mainframe-peru/types";
import { Middleware } from "@koa/router";
import { AppError } from "../../error";
import { activityLogQueue } from "../activityLogSQS";
import {
  ActivityLogEvent,
  ActivityLogRequest,
} from "@mainframe-peru/types/build/common";
import { AuthorizedContextState } from "./auth";
import { Auth, getClientTypeByIssuer } from "../../jwt";
import { SQSClient } from "@aws-sdk/client-sqs";
import { ZodError } from "zod";

export function sendActivityLogEventMiddleware(
  SQSClient: SQSClient,
  type: ActivityLogEvent,
): Middleware {
  const activityLogMiddleware: Middleware<
    AuthorizedContextState<Auth>
  > = async ({ ctx, next }) => {
    console.log("ENTRA A MIDDLEWARE: " + JSON.stringify(ctx.request));
    const client = ctx.state.auth;
    if (!client) {
      throw new AppError({
        code: "BadAuthorization",
        message: "No valid session was found.",
        statusCode: "FORBIDDEN",
        logLevel: "INFO",
      });
    }

    const clientType = getClientTypeByIssuer(
      client.iss as `mainframe:${common.ClientType}`,
    );
    console.log("clientType: " + clientType);

    const activityLog: ActivityLogRequest = {
      influencerId:
        client.iss === "mainframe:officer" ? "mainframe" : client.influencerId,
      clientType,
      clientId: client.id,
      type,
      status: "SUCCESS",
      request: ctx.request.body ?? ctx.request.query,
      timestamp: new Date(),
    };
    console.log("activityLog: " + JSON.stringify(activityLog));
    try {
      const r = await next();
      console.log("RR: " + JSON.stringify(r));
      await activityLogQueue.sendActivityLogEvent(SQSClient, activityLog);
    } catch (e) {
      let errorMessage;
      if (e instanceof ZodError) {
        errorMessage = "El request body no es válido: " + e.issues;
      } else if (e instanceof AppError) {
        errorMessage =
          e.code + ": " + e.message + ". Response Body: " + e.responseBody;
      }
      try{
        await activityLogQueue.sendActivityLogEvent(SQSClient, {
          ...activityLog,
          message: errorMessage,
          status: "FAIL",
        });
      } catch(f) {
        console.log("Failed to send activity log", f);
      }
    }
  };
  return activityLogMiddleware;
}
