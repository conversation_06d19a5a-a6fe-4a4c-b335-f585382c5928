import { common } from "@mainframe-peru/types";
import { Middleware } from "koa";
import { ZodError } from "zod";
import { AppError } from "../../error";
import { activityLogQueue } from "../activityLogSQS";
import { ActivityLogEvent, ActivityLogRequest } from "@mainframe-peru/types/build/common";
import { AuthorizedContextState } from "./auth";
import { Auth, getClientTypeByIssuer } from "../../jwt";
import {
  SQSClient,
} from "@aws-sdk/client-sqs";

export function sendActivityLogEventMiddleware(SQSClient: SQSClient, type: ActivityLogEvent): Middleware {
  const activityLogMiddleware: Middleware<AuthorizedContextState<Auth>> = async (ctx, next) => {
    const client = ctx.state.auth;
    if (!client) {
      throw new AppError({
        code: "BadAuthorization",
        message: "No valid session was found.",
        statusCode: "FORBIDDEN",
        logLevel: "INFO",
      });
    }

    const clientType = getClientTypeByIssuer(
      client.iss as `mainframe:${common.ClientType}`,
    );

    const activityLog: ActivityLogRequest = {
      type,
      clientType,
      clientId: client.id,
      status: "SUCCESS",
      timestamp: new Date(),
    };
    try {
      await next();     
      await activityLogQueue.sendActivityLogEvent(SQSClient, activityLog);
    } catch (e) {
      await activityLogQueue.sendActivityLogEvent(SQSClient, {
        ...activityLog,
        status: "FAIL",
      });
    }
  };
  return activityLogMiddleware;
}
