import { AdminPolicies, adminPoliciesConstant } from "../admin";
import { OfficerPolicies, officerPoliciesConstant } from "../officer";
import { UserPolicies, userPoliciesConstant } from "../user";

export type ClientPolicies = {
  officer: OfficerPolicies;
  admin: AdminPolicies;
  user: UserPolicies;
};

export const clientPoliciesConstant = {
  officer: officerPoliciesConstant,
  admin: adminPoliciesConstant,
  user: userPoliciesConstant,
};

export type ClientPoliciesIntersection =
  | OfficerPolicies
  | AdminPolicies
  | UserPolicies;

export type PermissionsNeededByClient = {
  [K in keyof ClientPolicies]?: {
    [M in keyof ClientPolicies[K]]?: ClientPolicies[K][M][];
  };
};
