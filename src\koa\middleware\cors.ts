import { Middleware } from "koa";

export function getCorsMiddleware(accepted: string[]): Middleware {
  return async (ctx, next) => {
    let i = 0;
    if (!ctx.request.headers.origin) return await next();
    while (
      !ctx.request.headers.origin.endsWith(accepted[i]) &&
      i < accepted.length
    )
      i++;

    if (i === accepted.length) return await next();

    ctx.response.set("access-control-allow-origin", `https://${accepted[i]}`);
    ctx.response.set("access-control-allow-credentials", "true");
    await next();
  };
}
