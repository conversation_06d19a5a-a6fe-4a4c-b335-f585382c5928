import { importPKCS8, import<PERSON><PERSON><PERSON>, SignJWT, jwtVerify, errors } from "jose";
import { AppError } from "../error";
import { common } from "@mainframe-peru/types";

type AuthBase = {
  exp: number;
};

export type AuthOfficer = {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  policies: Record<string, number>;
  iss: "mainframe:officer";
} & AuthBase;

export type AuthAdmin = {
  id: number;
  influencerId: string;
  email: string;
  firstName: string;
  lastName: string;
  policies: Record<string, number>;
  iss: "mainframe:admin";
} & AuthBase;

export type AuthEndUser = {
  id: number;
  influencerId: string;
  email: string;
  firstName: string;
  lastName: string;
  policies?: Record<string, number>;
  iss: "mainframe:user";
} & AuthBase;

export type Auth = AuthOfficer | AuthAdmin | AuthEndUser;

type AuthByType<T extends common.ClientType> = T extends "officer"
  ? AuthOfficer
  : T extends "admin"
    ? AuthAdmin
    : AuthEndUser;

function getIssuer(type: common.ClientType): `mainframe:${common.ClientType}` {
  if (type === "officer") return "mainframe:officer";
  else if (type === "admin") return "mainframe:admin";
  else return "mainframe:user";
}

export function getClientTypeByIssuer(
  issuer: `mainframe:${common.ClientType}`,
): common.ClientType {
  return issuer.split(":")[1] as common.ClientType;
}

export function getClientType(jwt: string): common.ClientType {
  try {
    const decodedJwt: Auth = JSON.parse(atob(jwt.split(".")[1]));
    return getClientTypeByIssuer(
      decodedJwt.iss as `mainframe:${common.ClientType}`,
    );
  } catch (e) {
    throw new AppError({
      code: "BadAuthorization",
      message: "No valid session was found.",
      statusCode: "FORBIDDEN",
      logLevel: "INFO",
    });
  }
}

export async function createJWT<T extends common.ClientType>(
  type: T,
  payload: Omit<AuthByType<T>, "iss" | "exp">,
  key: string,
  exp: number,
): Promise<string> {
  const alg = "RS256";
  const privateKey = await importPKCS8(key, alg);
  return await new SignJWT(payload)
    .setProtectedHeader({ alg })
    .setIssuedAt()
    .setIssuer(getIssuer(type))
    .setExpirationTime(exp)
    .sign(privateKey);
}

export async function verifyClientJWT(
  jwt: string,
  publicKey: string,
): Promise<Auth> {
  try {
    const clientType = getClientType(jwt);
    const { payload } = await jwtVerify<Auth>(
      jwt,
      await importSPKI(publicKey, "RS256"),
      {
        issuer: getIssuer(clientType),
      },
    );
    return payload;
  } catch (e) {
    throw new AppError({
      code: "InvalidJWT",
      message:
        e instanceof errors.JOSEError ? e.message : "Failed JWT validation.",
      statusCode: "UNAUTHORIZED",
      logLevel: "INFO",
    });
  }
}

type SetCookieHeaderInput<T extends common.ClientType> = {
  /**
   * Type of user
   */
  type: T;
  /**
   * JWT custom payload
   */
  payload: Omit<AuthByType<T>, "iss" | "exp">;
  /**
   * Private key used to create new token
   */
  key: string;
  /**
   * Duration in minutes
   */
  duration: number;
};

export async function getJWTSetCookieHeader<T extends common.ClientType>(
  input: SetCookieHeaderInput<T>,
): Promise<string> {
  const { duration, type, payload, key } = input;
  const exp = Math.floor(Date.now() / 1000) + duration * 60;
  const jwt = await createJWT(type, payload, key, exp);
  return `session=${jwt}; Max-Age=${exp}; Secure; SameSite=Strict; Path=/; HttpOnly`;
}

export const expirationCookieHeader = `session=""; Max-Age=0; Secure; SameSite=Strict; Path=/; HttpOnly`;

export function getSessionCookieValue(headerValue: string): string | undefined {
  const cookies = headerValue.split("; ");
  for (const c of cookies) {
    const [name, value] = c.split("=");
    if (name === "session") {
      return value;
    }
  }
  return;
}

export function getClientTypeFromCookie(
  headerValue: string | undefined,
): common.ClientType | undefined {
  if (!headerValue) {
    return;
  }
  const jwt = getSessionCookieValue(headerValue);
  if (jwt) return getClientType(jwt);
}

export function getSessionFromAuthorization(
  headerValue: string,
): string | undefined {
  const [bearer, value] = headerValue.split(" ");
  if (bearer !== "Bearer") {
    return;
  }
  return value;
}
