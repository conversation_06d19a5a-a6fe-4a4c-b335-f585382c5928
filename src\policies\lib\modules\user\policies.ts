import { PolicyModules } from "../../../../types/main.types";
import { UserPolicies } from "./types";

// Define the constant
export const userPoliciesConstant: PolicyModules<UserPolicies> = {
  general: {
    SUPERUSER: {
      value: 1 << 0,
      description:
        "El usuario tiene privilegios de superuser (advertencia: solo empleados de Mainframe)",
    },
    REGULAR: {
      value: 1 << 1,
      description: "El usuario es de tipo regular",
    },
    TRANSIENT: {
      value: 1 << 2,
      description: "El usuario no tiene contraseña",
    },
  },
};
